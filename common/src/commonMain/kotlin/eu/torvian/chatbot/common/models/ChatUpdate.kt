package eu.torvian.chatbot.common.models

import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import eu.torvian.chatbot.common.api.ApiError

/**
 * Represents a discrete update event sent during a streaming chat completion.
 * Used for server-to-client communication to incrementally build the chat message.
 */
@Serializable
sealed class ChatUpdate {
    /**
     * Sent once the user's message has been processed and saved on the server.
     */
    @Serializable
    data class UserMessageUpdate(val message: ChatMessage.UserMessage) : ChatUpdate()

    /**
     * Sent when the assistant's message starts, providing initial metadata and a temporary ID.
     * The content will be empty or a single token.
     * The `messageId` here is a client-side temporary ID.
     */
    @Serializable
    data class AssistantMessageStart(
        val messageId: Long, // Client-side temporary ID
        val parentId: Long,
        val sessionId: Long,
        val createdAt: Instant,
        val modelId: Long?,
        val settingsId: Long?
    ) : ChatUpdate()

    /**
     * Sent for each new content chunk from the LLM.
     * The client should append `deltaContent` to the existing message identified by `messageId`.
     * The `messageId` here is the client-side temporary ID.
     */
    @Serializable
    data class AssistantMessageDelta(val messageId: Long, val deltaContent: String) : ChatUpdate()

    /**
     * Sent when the assistant's message generation is complete.
     * Provides the temporary ID that was used and the final, persisted message with its real ID.
     */
    @Serializable
    data class AssistantMessageEnd(
        val tempMessageId: Long, // The temporary ID used during streaming
        val finalMessage: ChatMessage.AssistantMessage // The final, persisted message with its real ID
    ) : ChatUpdate()

    /**
     * Sent if an error occurs during the streaming process.
     */
    @Serializable
    data class ErrorUpdate(val error: ApiError) : ChatUpdate()

    /**
     * Sent as the final signal to indicate the end of the entire stream.
     */
    @Serializable
    data object Done : ChatUpdate()
}
