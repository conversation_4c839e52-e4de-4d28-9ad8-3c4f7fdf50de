### Common Module Files

**1. `chatbot/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ProcessNewMessageRequest.kt`**
(Added `stream` property)

```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable

/**
 * Request body for sending a new message to a chat session.
 *
 * @property content The user's message content.
 * @property parentMessageId The ID of the message this is a reply to (null for initial messages or if replying to the root of a new thread branch).
 * @property stream Whether the client requests a streaming response. Defaults to true.
 */
@Serializable
data class ProcessNewMessageRequest(
    val content: String,
    val parentMessageId: Long? = null,
    val stream: Boolean = true // Add this flag
)
```

**2. `chatbot/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatUpdate.kt`**
(New file for streaming DTOs)

```kotlin
package eu.torvian.chatbot.common.models

import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import eu.torvian.chatbot.common.api.ApiError // Import ApiError

/**
 * Represents a discrete update event sent during a streaming chat completion.
 * Used for server-to-client communication to incrementally build the chat message.
 */
@Serializable
sealed class ChatUpdate {
    /**
     * Sent once the user's message has been processed and saved on the server.
     */
    @Serializable
    data class UserMessageUpdate(val message: ChatMessage.UserMessage) : ChatUpdate()

    /**
     * Sent when the assistant's message starts, providing initial metadata and a temporary ID.
     * The content will be empty or a single token.
     * The `messageId` here is a client-side temporary ID.
     */
    @Serializable
    data class AssistantMessageStart(
        val messageId: Long, // Client-side temporary ID
        val parentId: Long,
        val sessionId: Long,
        val createdAt: Instant,
        val modelId: Long?,
        val settingsId: Long?
    ) : ChatUpdate()

    /**
     * Sent for each new content chunk from the LLM.
     * The client should append `deltaContent` to the existing message identified by `messageId`.
     * The `messageId` here is the client-side temporary ID.
     */
    @Serializable
    data class AssistantMessageDelta(val messageId: Long, val deltaContent: String) : ChatUpdate()

    /**
     * Sent when the assistant's message generation is complete.
     * Provides the temporary ID that was used and the final, persisted message with its real ID.
     */
    @Serializable
    data class AssistantMessageEnd(
        val tempMessageId: Long, // The temporary ID used during streaming
        val finalMessage: ChatMessage.AssistantMessage // The final, persisted message with its real ID
    ) : ChatUpdate()

    /**
     * Sent if an error occurs during the streaming process.
     */
    @Serializable
    data class ErrorUpdate(val error: ApiError) : ChatUpdate()

    /**
     * Sent as the final signal to indicate the end of the entire stream.
     */
    @Serializable
    data object Done : ChatUpdate()
}
```

---

### Server Module Files

**1. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMStreamChunk.kt`**
(New file for internal LLM streaming chunks)

```kotlin
package eu.torvian.chatbot.server.service.llm

/**
 * Sealed class representing a single chunk of data in a streaming LLM response.
 * This is an internal representation, parsed by strategies from raw API responses.
 */
sealed class LLMStreamChunk {
    /** Represents a content delta chunk. */
    data class ContentChunk(val deltaContent: String, val finishReason: String? = null) : LLMStreamChunk()

    /** Represents a usage statistics chunk (might be sent at the end). */
    data class UsageChunk(val promptTokens: Int, val completionTokens: Int, val totalTokens: Int) : LLMStreamChunk()

    /** Represents the final "done" signal from the LLM. */
    data object Done : LLMStreamChunk()

    /** Represents an error encountered *during* streaming by the LLM API itself. */
    data class Error(val llmError: LLMCompletionError) : LLMStreamChunk()
}
```

**2. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClient.kt`**
(Updated `completeChatStreaming` method)

```kotlin
package eu.torvian.chatbot.server.service.llm
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.ModelSettings
import kotlinx.coroutines.flow.Flow

/**
 * Interface for interacting with external LLM APIs.
 * Uses provider-agnostic generic models ([LLMCompletionResult], [LLMCompletionError])
 * for request results, hiding the provider-specific API details from the caller.
 */
interface LLMApiClient {
    /**
     * Sends a non-streaming chat completion request to the appropriate LLM API.
     * Delegates the API-specific details (request format, response parsing) to an internal strategy.
     *
     * @param messages The list of messages forming the conversation context.
     * @param modelConfig Configuration details for the target LLM model.
     * @param provider Provider configuration containing base URL and type information.
     * @param settings Specific settings profile to use for this completion request.
     * @param apiKey The decrypted API key for authentication (nullable if not required by the provider).
     * @return Either an [LLMCompletionError] if the process fails at any stage (configuration, network, API error, parsing),
     *         or the generic [LLMCompletionResult] on success.
     */
    suspend fun completeChat(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?
    ): Either<LLMCompletionError, LLMCompletionResult>

    /**
     * Sends a streaming chat completion request to the appropriate LLM API.
     * Delegates API-specific details to a strategy.
     *
     * @return A Flow of Either<LLMCompletionError, LLMStreamChunk> representing the stream.
     *         An error emitted in the Flow indicates a problem during the stream.
     *         The flow terminates with `LLMStreamChunk.Done` on success or an error.
     */
    fun completeChatStreaming(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?
    ): Flow<Either<LLMCompletionError, LLMStreamChunk>>
}
```

**3. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/ChatCompletionStrategy.kt`**
(Updated `prepareRequest` and added `processStreamingResponse`)

```kotlin
package eu.torvian.chatbot.server.service.llm
import arrow.core.Either
import eu.torvian.chatbot.common.models.*
import kotlinx.coroutines.flow.Flow

/**
 * Defines the interface for provider-specific chat completion logic.
 * Each implementation handles the API details for a particular LLMProviderType,
 * mapping between generic application models and provider-specific API formats.
 * Strategies should be independent of the HTTP client implementation (e.g., Ktor).
 */
interface ChatCompletionStrategy {
    /**
     * Prepares the raw data structure and configuration for the API request.
     *
     * @param isStreaming Boolean indicating if a streaming response is requested.
     * @return Either a [LLMCompletionError.ConfigurationError] if preparation fails,
     *         or the [ApiRequestConfig] containing details for the HTTP call.
     */
    fun prepareRequest(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?,
        isStreaming: Boolean // ADDED PARAMETER
    ): Either<LLMCompletionError.ConfigurationError, ApiRequestConfig>

    /**
     * Processes a raw successful API response body string into the generic result ([LLMCompletionResult]).
     * This is for **non-streaming** responses.
     */
    fun processSuccessResponse(responseBody: String): Either<LLMCompletionError.InvalidResponseError, LLMCompletionResult>

    /**
     * Processes a raw streaming API response (as a Flow of strings/bytes) into a generic stream of LLMStreamChunk.
     * The strategy is responsible for parsing each raw chunk according to its provider's streaming format
     * (e.g., SSE for OpenAI, NDJSON for Ollama) and mapping it to LLMStreamChunk.
     *
     * @param responseStream A Flow of raw string chunks from the HTTP response.
     * @return A Flow of Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>
     */
    fun processStreamingResponse(
        responseStream: Flow<String> // Or Flow<ByteReadPacket> if you need more control
    ): Flow<Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>>

    /**
     * Processes a raw error API response body string and status code into a specific generic error ([LLMCompletionError]).
     */
    fun processErrorResponse(statusCode: Int, errorBody: String): LLMCompletionError

    /**
     * Identifies the [LLMProviderType] this strategy handles. Used by the client to select the correct strategy.
     */
    val providerType: LLMProviderType
}
```

**4. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaApiModels.kt`**
(Added `ChatCompletionStreamResponse` for parsing streaming chunks)

```kotlin
package eu.torvian.chatbot.server.service.llm.strategy
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Data Transfer Objects for Ollama API responses and requests.
 * These models represent the structure of communication with Ollama's local API.
 * Based on Ollama API documentation: https://ollama.readthedocs.io/en/api/
 */
object OllamaApiModels {
    /**
     * Represents a chat completion response from Ollama API.
     * Matches the structure documented by Ollama for non-streaming responses.
     *
     * @property model The model name used by the API
     * @property created_at ISO 8601 timestamp of when the completion was created
     * @property message The generated message
     * @property done Whether the response is complete
     * @property total_duration Total time spent generating the response (nanoseconds)
     * @property load_duration Time spent loading the model (nanoseconds)
     * @property prompt_eval_count Number of tokens in the prompt
     * @property prompt_eval_duration Time spent evaluating the prompt (nanoseconds)
     * @property eval_count Number of tokens in the response
     * @property eval_duration Time spent generating the response (nanoseconds)
     */
    @Serializable
    data class ChatCompletionResponse(
        val model: String,
        val created_at: String,
        val message: Message,
        val done: Boolean,
        val total_duration: Long? = null,
        val load_duration: Long? = null,
        val prompt_eval_count: Int? = null,
        val prompt_eval_duration: Long? = null,
        val eval_count: Int? = null,
        val eval_duration: Long? = null
    ) {
        /**
         * Represents a message within the chat completion response.
         * Contains the role and content of the generated message.
         *
         * @property role Role string (e.g., "assistant")
         * @property content The generated text
         */
        @Serializable
        data class Message(
            val role: String,
            val content: String
        )
    }

    /**
     * Represents a single chunk received during a streaming chat completion from Ollama.
     * Each line in an Ollama stream is a JSON object matching this structure (except for the final 'done: true' object).
     * The 'done' property indicates if the stream is finished.
     * The 'message' property contains the delta content.
     */
    @Serializable
    data class ChatCompletionStreamResponse(
        val model: String,
        val created_at: String,
        val message: Message? = null, // message can be null for the final done chunk (which contains stats)
        val done: Boolean,
        val total_duration: Long? = null,
        val load_duration: Long? = null,
        val prompt_eval_count: Int? = null,
        val prompt_eval_duration: Long? = null,
        val eval_count: Int? = null,
        val eval_duration: Long? = null
    ) {
        @Serializable
        data class Message(
            val role: String,
            val content: String
        )
    }

    /**
     * Represents a chat completion request to Ollama API.
     * Matches the structure documented by Ollama for non-streaming requests.
     *
     * @property model The specific model name string (e.g., "llama3.2")
     * @property messages The conversation history
     * @property stream Whether to stream the response (default: false for non-streaming)
     * @property options Additional model parameters
     */
    @Serializable
    data class ChatCompletionRequest(
        val model: String,
        val messages: List<RequestMessage>,
        val stream: Boolean = false, // Default is false, but overridden by prepareRequest
        val options: Options? = null
    ) {
        /**
         * Represents a message in the chat completion request.
         * Role string must match Ollama API expectations ("system", "user", "assistant").
         *
         * @property role Role string (e.g., "system", "user", "assistant")
         * @property content Message content
         */
        @Serializable
        data class RequestMessage(
            val role: String,
            val content: String
        )
        /**
         * Represents additional options for the Ollama model.
         * These correspond to the model parameters that can be set.
         *
         * @property temperature Sampling temperature
         * @property top_p Nucleus sampling
         * @property top_k Top-k sampling
         * @property num_predict Maximum number of tokens to generate
         * @property stop Stop sequences
         * @property seed Random seed for reproducible outputs
         */
        @Serializable
        data class Options(
            val temperature: Float? = null,
            val top_p: Float? = null,
            val top_k: Int? = null,
            val num_predict: Int? = null,
            val stop: List<String>? = null,
            val seed: Int? = null
        )
    }

    /**
     * Represents the common structure of an error response from Ollama.
     * Used by strategies to parse error details from raw error bodies.
     *
     * @property error The error message
     */
    @Serializable
    data class OllamaErrorResponse(
        val error: String
    )
}
```

**5. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaChatStrategy.kt`**
(Updated `prepareRequest` and `processStreamingResponse`)

```kotlin
package eu.torvian.chatbot.server.service.llm.strategy
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.llm.*
import kotlinx.coroutines.flow.*
import kotlinx.serialization.json.*
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
/**
 * Chat completion strategy for Ollama API.
 * Handles mapping generic ChatMessage/ModelSettings to Ollama API request format,
 * and mapping Ollama API response format to generic LLMCompletionResult.
 * Depends on kotlinx.serialization.Json to parse raw response strings.
 *
 * @property json The Json instance used for serialization/deserialization
 */
class OllamaChatStrategy(private val json: Json) : ChatCompletionStrategy {
    private val logger: Logger = LogManager.getLogger(OllamaChatStrategy::class.java)
    override val providerType: LLMProviderType = LLMProviderType.OLLAMA

    override fun prepareRequest(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?,
        isStreaming: Boolean // ADDED PARAMETER
    ): Either<LLMCompletionError.ConfigurationError, ApiRequestConfig> {
        logger.debug("OllamaChatStrategy: Preparing request for model ${modelConfig.name} (isStreaming: $isStreaming)")
        // 1. Ollama typically doesn't require an API key for local instances
        // However, we don't validate against apiKey being provided as some setups might use it
        logger.debug("OllamaChatStrategy: API key requirement check - Ollama typically runs locally without API key")
        // 2. Map generic ChatMessage list to Ollama API specific RequestMessage list
        fun ChatMessage.toOllamaApiMessage(): OllamaApiModels.ChatCompletionRequest.RequestMessage {
            return OllamaApiModels.ChatCompletionRequest.RequestMessage(
                role = when (this.role) {
                    ChatMessage.Role.USER -> "user"
                    ChatMessage.Role.ASSISTANT -> "assistant"
                },
                content = this.content
            )
        }
        // Add system message if present in settings
        val systemMessage = settings.systemMessage
        val apiMessages = buildList {
            if (!systemMessage.isNullOrBlank()) {
                add(OllamaApiModels.ChatCompletionRequest.RequestMessage(role = "system", content = systemMessage))
            }
            // Add user and assistant messages from the conversation history
            addAll(messages.map { it.toOllamaApiMessage() })
        }
        // 3. Map ModelSettings to Ollama API request parameters
        val customParams: JsonObject? = settings.customParamsJson?.let { customJson ->
            val element = try {
                json.parseToJsonElement(customJson)
            } catch (e: Exception) {
                throw IllegalStateException("Failed to parse customParamsJson for Ollama request", e)
            }
            // Ensure the parsed element is a JsonObject
            if (element !is JsonObject) {
                throw IllegalStateException("customParamsJson for Ollama request is not a JSON object")
            }
            element
        }
        // Safely extract parameters using JsonObject accessors
        val topP = customParams?.get("top_p")?.jsonPrimitive?.floatOrNull
        val topK = customParams?.get("top_k")?.jsonPrimitive?.intOrNull
        val stop = customParams?.get("stop")?.jsonArray?.mapNotNull { it.jsonPrimitive.contentOrNull }
        val seed = customParams?.get("seed")?.jsonPrimitive?.intOrNull
        // Build the Ollama specific options
        val options = OllamaApiModels.ChatCompletionRequest.Options(
            temperature = settings.temperature,
            num_predict = settings.maxTokens,
            top_p = topP,
            top_k = topK,
            stop = stop,
            seed = seed
        )
        // Build the Ollama specific request DTO (@Serializable object)
        val requestBodyDto = OllamaApiModels.ChatCompletionRequest(
            model = modelConfig.name,
            messages = apiMessages,
            stream = isStreaming, // USE THE PARAMETER
            options = options
        )
        // 4. Determine API specific path and headers
        val path = "/api/chat"
        val customHeaders = mutableMapOf<String, String>()
        // Ollama typically doesn't require authorization headers for local instances
        // If an API key is provided, we could add it, but it's not standard for Ollama
        logger.debug("OllamaChatStrategy: No authorization header needed for local Ollama instance")
        logger.debug(
            "OllamaChatStrategy: Prepared request body: ${
                json.encodeToString(requestBodyDto).take(500)
            }..."
        ) // Log part of the body
        // 5. Return the generic ApiRequestConfig
        return ApiRequestConfig(
            path = path,
            method = GenericHttpMethod.POST,
            body = requestBodyDto,
            contentType = GenericContentType.APPLICATION_JSON,
            customHeaders = customHeaders
        ).right()
    }

    override fun processSuccessResponse(responseBody: String): Either<LLMCompletionError.InvalidResponseError, LLMCompletionResult> {
        // This method is for *non-streaming* responses, typically used by completeChat.
        logger.debug("OllamaChatStrategy: Processing success response body (non-streaming): ${responseBody.take(500)}...")
        return try {
            val successResponse: OllamaApiModels.ChatCompletionResponse = json.decodeFromString(responseBody)
            val result = LLMCompletionResult(
                id = null,
                choices = listOf(
                    LLMCompletionResult.CompletionChoice(
                        role = successResponse.message.role,
                        content = successResponse.message.content,
                        finishReason = if (successResponse.done) "stop" else null,
                        index = 0
                    )
                ),
                usage = LLMCompletionResult.UsageStats(
                    promptTokens = successResponse.prompt_eval_count ?: 0,
                    completionTokens = successResponse.eval_count ?: 0,
                    totalTokens = (successResponse.prompt_eval_count ?: 0) + (successResponse.eval_count ?: 0)
                ),
                metadata = mapOf(
                    "api_model" to successResponse.model,
                    "api_created_at" to successResponse.created_at,
                    "api_done" to successResponse.done,
                    "total_duration" to successResponse.total_duration,
                    "load_duration" to successResponse.load_duration,
                    "prompt_eval_duration" to successResponse.prompt_eval_duration,
                    "eval_duration" to successResponse.eval_duration
                ).filterValues { it != null }
            )
            logger.debug("OllamaChatStrategy: Successfully processed non-streaming response to LLMCompletionResult")
            result.right()
        } catch (e: Exception) {
            logger.error("OllamaChatStrategy: Failed to parse Ollama non-streaming success response body", e)
            LLMCompletionError.InvalidResponseError("Failed to parse Ollama non-streaming success response body: ${e.message}", e).left()
        }
    }

    override fun processStreamingResponse(
        responseStream: Flow<String>
    ): Flow<Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>> = flow {
        responseStream.collect { rawChunk ->
            if (rawChunk.isBlank()) return@collect // Ignore empty lines

            try {
                // Ollama sends newline-delimited JSON objects
                val streamResponse = json.decodeFromString<OllamaApiModels.ChatCompletionStreamResponse>(rawChunk)

                if (streamResponse.done) {
                    // This is the final chunk, might contain usage stats
                    val usage = LLMCompletionResult.UsageStats(
                        promptTokens = streamResponse.prompt_eval_count ?: 0,
                        completionTokens = streamResponse.eval_count ?: 0,
                        totalTokens = (streamResponse.prompt_eval_count ?: 0) + (streamResponse.eval_count ?: 0)
                    )
                    emit(LLMStreamChunk.UsageChunk(usage.promptTokens, usage.completionTokens, usage.totalTokens).right())
                    emit(LLMStreamChunk.Done.right())
                } else {
                    // Regular content chunk
                    streamResponse.message?.let { message ->
                        emit(LLMStreamChunk.ContentChunk(message.content).right())
                    } ?: run {
                        // This case should ideally not happen for non-done chunks, but for safety
                        logger.warn("OllamaChatStrategy: Received a non-done chunk without a 'message' field: $rawChunk")
                    }
                }
            } catch (e: Exception) {
                logger.error("OllamaChatStrategy: Failed to parse Ollama streaming response chunk: $rawChunk", e)
                emit(LLMCompletionError.InvalidResponseError("Failed to parse Ollama streaming response chunk: ${e.message}", e).left())
            }
        }
    }

    override fun processErrorResponse(statusCode: Int, errorBody: String): LLMCompletionError {
        logger.debug("OllamaChatStrategy: Processing error response body (Status $statusCode): ${errorBody.take(500)}...")
        val apiErrorMessage = try {
            val errorResponse = json.decodeFromString<OllamaApiModels.OllamaErrorResponse>(errorBody)
            errorResponse.error
        } catch (e: Exception) {
            logger.warn("OllamaChatStrategy: Failed to parse Ollama specific error body, using raw body.", e)
            errorBody.take(200)
        }
        return when (statusCode) {
            400 -> LLMCompletionError.ApiError(statusCode, "Ollama API bad request: $apiErrorMessage", errorBody)
            404 -> LLMCompletionError.ApiError(statusCode, "Ollama API model or endpoint not found: $apiErrorMessage", errorBody)
            500 -> LLMCompletionError.ApiError(statusCode, "Ollama API internal server error: $apiErrorMessage", errorBody)
            else -> LLMCompletionError.ApiError(statusCode, "Ollama API returned error $statusCode: $apiErrorMessage", errorBody)
        }
    }
}
```

**6. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClientKtor.kt`**
(Updated `completeChat` and `completeChatStreaming` to use `isStreaming` param)

```kotlin
package eu.torvian.chatbot.server.service.llm
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.*
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.utils.io.core.*
import io.ktor.utils.io.*
import kotlinx.coroutines.flow.*
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
/**
 * Ktor implementation of the [LLMApiClient].
 * This class handles the actual HTTP communication using Ktor.
 * It delegates the provider-specific request preparation and response processing
 * to injected [ChatCompletionStrategy] instances.
 *
 * Requires HttpClient to be configured with the ContentNegotiation feature
 * and appropriate serializers (e.g., KotlinxSerializationConverter for JSON).
 *
 * @property httpClient Injected Ktor HttpClient (configured for serialization)
 * @property strategies Injected map of strategies
 */
class LLMApiClientKtor(
    private val httpClient: HttpClient,
    private val strategies: Map<LLMProviderType, ChatCompletionStrategy>
) : LLMApiClient {
    companion object {
        private val logger: Logger = LogManager.getLogger(LLMApiClientKtor::class.java)
    }

    override suspend fun completeChat(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?
    ): Either<LLMCompletionError, LLMCompletionResult> {
        logger.info("LLMApiClientKtor: Received request for model ${modelConfig.name} (Provider: ${provider.name}, Type: ${provider.type}) (NON-STREAMING)")
        logger.debug("Context messages received: ${messages.size}")

        val strategy = strategies[provider.type]
            ?: run {
                val errorMsg = "No ChatCompletionStrategy found for provider type: ${provider.type}"
                logger.error(errorMsg)
                return LLMCompletionError.ConfigurationError(errorMsg).left()
            }

        val apiRequestConfig = strategy.prepareRequest(
            messages = messages,
            modelConfig = modelConfig,
            provider = provider,
            settings = settings,
            apiKey = apiKey,
            isStreaming = false // EXPLICITLY SET TO FALSE FOR NON-STREAMING
        ).getOrElse { error ->
            logger.error("Strategy ${strategy::class.simpleName} failed to prepare request: ${error.message}")
            return error.left()
        }

        try {
            logger.debug("Executing HTTP request: ${apiRequestConfig.method} ${provider.baseUrl}${apiRequestConfig.path}")
            val httpResponse: HttpResponse = httpClient.request {
                method = apiRequestConfig.method.toKtorHttpMethod()
                url("${provider.baseUrl}${apiRequestConfig.path}")
                contentType(apiRequestConfig.contentType.toKtorContentType())
                headers {
                    apiRequestConfig.customHeaders.forEach { (key, value) ->
                        append(key, value)
                    }
                }
                setBody(apiRequestConfig.body)
            }

            logger.debug("Received HTTP response: ${httpResponse.status}")
            val responseBody = try {
                httpResponse.bodyAsText()
            } catch (e: Exception) {
                logger.error("Failed to read response body from ${provider.name} API", e)
                return LLMCompletionError.NetworkError("Failed to read response body from ${provider.name}", e).left()
            }
            logger.debug("Response body read successfully (length: ${responseBody.length})")

            return if (httpResponse.status.isSuccess()) {
                logger.debug("Processing successful response with strategy ${strategy::class.simpleName}")
                strategy.processSuccessResponse(responseBody)
                    .getOrElse { error ->
                        logger.error(
                            "Strategy ${strategy::class.simpleName} failed to process success response: ${error.message}",
                            error.cause
                        )
                        return error.left()
                    }
                    .right()
            } else {
                logger.debug("Processing error response with strategy ${strategy::class.simpleName}")
                val apiError = strategy.processErrorResponse(httpResponse.status.value, responseBody)
                logger.error("LLM API ${provider.name} returned error (Status: ${httpResponse.status.value}): $apiError")
                return apiError.left()
            }
        } catch (e: Exception) {
            logger.error("LLMApiClientKtor: HTTP request failed for provider ${provider.name}", e)
            return LLMCompletionError.NetworkError(
                "Network or communication error with ${provider.name}: ${e.message}",
                e
            ).left()
        }
    }

    override fun completeChatStreaming(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?
    ): Flow<Either<LLMCompletionError, LLMStreamChunk>> = channelFlow {
        logger.info("LLMApiClientKtor: Received streaming request for model ${modelConfig.name} (Provider: ${provider.name}, Type: ${provider.type})")

        val strategy = strategies[provider.type]
            ?: run {
                val errorMsg = "No ChatCompletionStrategy found for provider type: ${provider.type}"
                logger.error(errorMsg)
                send(LLMCompletionError.ConfigurationError(errorMsg).left())
                close()
                return@channelFlow
            }

        val apiRequestConfig = strategy.prepareRequest(
            messages = messages,
            modelConfig = modelConfig,
            provider = provider,
            settings = settings,
            apiKey = apiKey,
            isStreaming = true // EXPLICITLY SET TO TRUE FOR STREAMING
        ).getOrElse { error ->
            logger.error("Strategy ${strategy::class.simpleName} failed to prepare streaming request: ${error.message}")
            send(error.left())
            close()
            return@channelFlow
        }

        try {
            logger.debug("Executing HTTP streaming request: ${apiRequestConfig.method} ${provider.baseUrl}${apiRequestConfig.path}")
            val httpResponse: HttpResponse = httpClient.request {
                method = apiRequestConfig.method.toKtorHttpMethod()
                url("${provider.baseUrl}${apiRequestConfig.path}")
                contentType(apiRequestConfig.contentType.toKtorContentType())
                headers {
                    apiRequestConfig.customHeaders.forEach { (key, value) ->
                        append(key, value)
                    }
                }
                setBody(apiRequestConfig.body)
                timeout {
                    requestTimeoutMillis = HttpTimeout.INFINITE_TIMEOUT_MS // Allow indefinite stream
                }
            }

            if (httpResponse.status.isSuccess()) {
                logger.debug("Received HTTP streaming response: ${httpResponse.status}")
                // Read the response body as a stream of lines
                val responseStream: Flow<String> = httpResponse.bodyAsFlow<ByteReadPacket>()
                    .map { it.readText() } // Convert ByteReadPacket to String
                    .transform { chunk -> // Split by newlines if a chunk contains multiple lines
                        chunk.lines().forEach { line ->
                            emit(line)
                        }
                    }

                // Process the stream using the strategy
                strategy.processStreamingResponse(responseStream).collect { chunkEither ->
                    send(chunkEither) // Forward each processed chunk to the downstream flow
                }
            } else {
                val errorBody = try {
                    httpResponse.bodyAsText()
                } catch (e: Exception) {
                    logger.error("Failed to read error response body from ${provider.name} API", e)
                    "Failed to read error body: ${e.message}"
                }
                logger.debug("Processing error response (streaming) with strategy ${strategy::class.simpleName}")
                val apiError = strategy.processErrorResponse(httpResponse.status.value, errorBody)
                logger.error("LLM API ${provider.name} returned error (Streaming Status: ${httpResponse.status.value}): $apiError")
                send(apiError.left())
            }
        } catch (e: Exception) {
            logger.error("LLMApiClientKtor: HTTP streaming request failed for provider ${provider.name}", e)
            send(LLMCompletionError.NetworkError(
                "Network or communication error with ${provider.name}: ${e.message}",
                e
            ).left())
        } finally {
            close() // Ensure the channel is closed
        }
    }
}

// --- Helper functions ---
/**
 * Converts a [GenericHttpMethod] to a Ktor [HttpMethod].
 */
private fun GenericHttpMethod.toKtorHttpMethod(): HttpMethod = when (this) {
    GenericHttpMethod.GET -> HttpMethod.Get
    GenericHttpMethod.POST -> HttpMethod.Post
    GenericHttpMethod.PUT -> HttpMethod.Put
    GenericHttpMethod.DELETE -> HttpMethod.Delete
    GenericHttpMethod.PATCH -> HttpMethod.Patch
    GenericHttpMethod.HEAD -> HttpMethod.Head
    GenericHttpMethod.OPTIONS -> HttpMethod.Options
}
/**
 * Converts a [GenericContentType] to a Ktor [ContentType].
 */
private fun GenericContentType.toKtorContentType(): ContentType = ContentType.parse(this.contentType)
```

**7. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/MessageDao.kt`**
(Removed `updateMessageContentAppend` and `updateAssistantMessageFinalize`)

```kotlin
package eu.torvian.chatbot.server.data.dao
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.server.data.dao.error.InsertMessageError
import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
import eu.torvian.chatbot.server.data.dao.error.MessageError

/**
 * Data Access Object (DAO) interface for Chat Messages.
 * Provides abstract methods for CRUD operations on message entities in the database.
 * Implementations should handle database-specific details.
 */
interface MessageDao {
    /**
     * Retrieves a list of all messages for a specific session, ordered by creation time.
     * @param sessionId The ID of the session.
     * @return A list of [ChatMessage] objects.
     */
    suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage>

    /**
     * Retrieves a message by its ID.
     * @param id The ID of the message to retrieve.
     * @return Either a [MessageError.MessageNotFound] or the [ChatMessage] object.
     */
    suspend fun getMessageById(id: Long): Either<MessageError.MessageNotFound, ChatMessage>

    /**
     * Inserts a new user message into the database.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to (null for root messages).
     * @return Either an [InsertMessageError] if insertion fails (e.g., session not found, parent not in session),
     *         or the newly created [ChatMessage.UserMessage].
     */
    suspend fun insertUserMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<InsertMessageError, ChatMessage.UserMessage>

    /**
     * Inserts a new assistant message into the database.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The assistant's message content.
     * @param parentMessageId ID of the user message this assistant message is a response to.
     * @param modelId The ID of the LLM model used for this message.
     * @param settingsId The ID of the settings profile used for this message.
     * @return Either an [InsertMessageError] if insertion fails,
     *         or the newly created [ChatMessage.AssistantMessage].
     */
    suspend fun insertAssistantMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long, // Changed to non-nullable as it's a response to a user message
        modelId: Long?,
        settingsId: Long?
    ): Either<InsertMessageError, ChatMessage.AssistantMessage>

    /**
     * Updates the content of an existing message.
     * @param id The ID of the message to update.
     * @param content The new content.
     * @return Either a [MessageError.MessageNotFound] or the updated [ChatMessage].
     */
    suspend fun updateMessageContent(id: Long, content: String): Either<MessageError.MessageNotFound, ChatMessage>

    /**
     * Deletes a specific message and its children recursively.
     * Updates the parent's children list.
     * @param id The ID of the message to delete.
     * @return Either a [MessageError.MessageNotFound] or Unit if successful.
     */
    suspend fun deleteMessage(id: Long): Either<MessageError.MessageNotFound, Unit>

    /**
     * Adds a child message ID to a parent message's children list.
     * @param parentId The ID of the parent message.
     * @param childId The ID of the child message to add.
     * @return Either a [MessageAddChildError] or Unit if successful.
     */
    suspend fun addChildToMessage(parentId: Long, childId: Long): Either<MessageAddChildError, Unit>
}
```

**8. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/exposed/MessageDaoExposed.kt`**
(Removed implementations of removed DAO methods)

```kotlin
package eu.torvian.chatbot.server.data.dao.exposed
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.raise.Raise
import arrow.core.raise.catch
import arrow.core.raise.either
import arrow.core.raise.ensure
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.error.InsertMessageError
import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
import eu.torvian.chatbot.server.data.dao.error.MessageError
import eu.torvian.chatbot.server.data.tables.AssistantMessageTable
import eu.torvian.chatbot.server.data.tables.ChatMessageTable
import eu.torvian.chatbot.server.data.tables.mappers.toAssistantMessage
import eu.torvian.chatbot.server.data.tables.mappers.toUserMessage
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import kotlinx.datetime.Instant
import kotlinx.serialization.json.Json
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
/**
 * Exposed implementation of the [MessageDao].
 */
class MessageDaoExposed(
    private val transactionScope: TransactionScope
) : MessageDao {
    companion object {
        private val logger: Logger = LogManager.getLogger(MessageDaoExposed::class.java)
    }

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> =
        transactionScope.transaction {
            // Perform a single query with left join to get all messages with their assistant data if any
            val results = ChatMessageTable
                .leftJoin(AssistantMessageTable, { ChatMessageTable.id }, { AssistantMessageTable.messageId })
                .selectAll()
                .where { ChatMessageTable.sessionId eq sessionId }
                .orderBy(ChatMessageTable.createdAt to SortOrder.ASC)
            // Transform to appropriate message types based on role
            results.map { row ->
                when (row[ChatMessageTable.role]) {
                    ChatMessage.Role.USER -> row.toUserMessage()
                    ChatMessage.Role.ASSISTANT -> row.toAssistantMessage()
                }
            }
        }

    override suspend fun getMessageById(id: Long): Either<MessageError.MessageNotFound, ChatMessage> =
        transactionScope.transaction {
            either {
                // Perform a single query with left join to get all needed data
                val query = ChatMessageTable
                    .leftJoin(AssistantMessageTable, { ChatMessageTable.id }, { AssistantMessageTable.messageId })
                    .selectAll()
                    .where { ChatMessageTable.id eq id }
                    .singleOrNull()
                ensure(query != null) { MessageError.MessageNotFound(id) }
                // Convert to appropriate message type based on role
                when (query[ChatMessageTable.role]) {
                    ChatMessage.Role.USER -> query.toUserMessage()
                    ChatMessage.Role.ASSISTANT -> query.toAssistantMessage()
                }
            }
        }

    override suspend fun insertUserMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<InsertMessageError, ChatMessage.UserMessage> =
        transactionScope.transaction {
            either {
                if (parentMessageId != null) {
                    verifyParentInSession(parentMessageId, sessionId)
                }
                catch({
                    val now = System.currentTimeMillis()
                    val insertStatement = ChatMessageTable.insert {
                        it[ChatMessageTable.sessionId] = sessionId
                        it[ChatMessageTable.role] = ChatMessage.Role.USER
                        it[ChatMessageTable.content] = content
                        it[ChatMessageTable.createdAt] = now
                        it[ChatMessageTable.updatedAt] = now
                        it[ChatMessageTable.parentMessageId] = parentMessageId
                        it[ChatMessageTable.childrenMessageIds] = Json.encodeToString(emptyList<Long>())
                    }
                    insertStatement.resultedValues?.first()?.toUserMessage()
                        ?: throw IllegalStateException("Failed to retrieve newly inserted user message")
                }) { e: ExposedSQLException ->
                    logger.error(
                        "Failed to insert user message for session $sessionId and parent $parentMessageId", e
                    )
                    ensure(!e.isForeignKeyViolation()) { InsertMessageError.SessionNotFound(sessionId) }
                    throw e
                }
            }
        }

    override suspend fun insertAssistantMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long, // Changed to non-nullable
        modelId: Long?,
        settingsId: Long?
    ): Either<InsertMessageError, ChatMessage.AssistantMessage> =
        transactionScope.transaction {
            either {
                // No need to verify parent in session here, as it's handled by service logic that calls this after user message
                catch({
                    val now = System.currentTimeMillis()
                    val insertStatement = ChatMessageTable.insert {
                        it[ChatMessageTable.sessionId] = sessionId
                        it[ChatMessageTable.role] = ChatMessage.Role.ASSISTANT
                        it[ChatMessageTable.content] = content
                        it[ChatMessageTable.createdAt] = now
                        it[ChatMessageTable.updatedAt] = now
                        it[ChatMessageTable.parentMessageId] = parentMessageId
                        it[ChatMessageTable.childrenMessageIds] = Json.encodeToString(emptyList<Long>())
                    }
                    val messageRow = insertStatement.resultedValues?.first()
                        ?: throw IllegalStateException("Failed to retrieve newly inserted assistant message")
                    // Insert into AssistantMessages table
                    AssistantMessageTable.insert {
                        it[AssistantMessageTable.messageId] = messageRow[ChatMessageTable.id].value
                        it[AssistantMessageTable.modelId] = modelId
                        it[AssistantMessageTable.settingsId] = settingsId
                    }
                    // Return AssistantMessage object
                    ChatMessage.AssistantMessage(
                        id = messageRow[ChatMessageTable.id].value,
                        sessionId = messageRow[ChatMessageTable.sessionId].value,
                        content = messageRow[ChatMessageTable.content],
                        createdAt = Instant.fromEpochMilliseconds(messageRow[ChatMessageTable.createdAt]),
                        updatedAt = Instant.fromEpochMilliseconds(messageRow[ChatMessageTable.updatedAt]),
                        parentMessageId = messageRow[ChatMessageTable.parentMessageId]?.value,
                        childrenMessageIds = emptyList(),
                        modelId = modelId,
                        settingsId = settingsId
                    )
                }) { e: ExposedSQLException ->
                    logger.error(
                        "Failed to insert assistant message for session $sessionId and parent $parentMessageId", e
                    )
                    // If foreign key violation, it might be the parent not existing.
                    // For streaming, we assume parent user message is already saved.
                    // If a foreign key violation occurs here, it's an unexpected state.
                    ensure(!e.isForeignKeyViolation()) { InsertMessageError.SessionNotFound(sessionId) } // Re-throw or map specifically
                    throw e
                }
            }
        }

    override suspend fun updateMessageContent(
        id: Long,
        content: String
    ): Either<MessageError.MessageNotFound, ChatMessage> =
        transactionScope.transaction {
            either {
                val updatedRowCount = ChatMessageTable.update({ ChatMessageTable.id eq id }) {
                    it[ChatMessageTable.content] = content
                    it[ChatMessageTable.updatedAt] = System.currentTimeMillis()
                }
                ensure(updatedRowCount != 0) { MessageError.MessageNotFound(id) }
                // Retrieve the updated message
                getMessageById(id).getOrElse { throw IllegalStateException("Failed to retrieve updated message") }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<MessageError.MessageNotFound, Unit> =
        transactionScope.transaction {
            either {
                logger.debug("DAO: Deleting message with ID $id")
                // Get the full message to access its children IDs
                val message = getMessageById(id).bind()
                // Recursively delete children
                for (childId in message.childrenMessageIds) {
                    deleteRecursively(childId)
                }
                // Delete the base message
                val deletedCount = ChatMessageTable.deleteWhere { ChatMessageTable.id eq id }
                if (deletedCount == 0)
                    throw IllegalStateException("Failed to delete message with ID $id")
                // If this message had a parent, update the parent's children list
                val parentId = message.parentMessageId
                if (parentId != null) {
                    val parentRow = ChatMessageTable
                        .selectAll().where { ChatMessageTable.id eq parentId }.singleOrNull()
                    if (parentRow == null) {
                        throw IllegalStateException("Parent message with ID $parentId not found when deleting child $id")
                    }
                    val currentChildrenIdsString = parentRow[ChatMessageTable.childrenMessageIds]
                    val childrenList = Json.decodeFromString<MutableList<Long>>(currentChildrenIdsString)
                    if (!childrenList.remove(id)) {
                        throw IllegalStateException("Child ID $id not found in parent ID $parentId children list")
                    }
                    val newChildrenIdsString = Json.encodeToString(childrenList)
                    val updatedRowCount = ChatMessageTable.update({ ChatMessageTable.id eq parentId }) {
                        it[ChatMessageTable.childrenMessageIds] = newChildrenIdsString
                    }
                    if (updatedRowCount == 0) {
                        throw IllegalStateException("Failed to update parent message with ID $parentId when deleting child $id")
                    }
                }
                logger.debug("DAO: Successfully deleted message with ID $id")
            }
        }

    override suspend fun addChildToMessage(parentId: Long, childId: Long): Either<MessageAddChildError, Unit> =
        transactionScope.transaction {
            either {
                logger.debug("DAO: Adding child message ID $childId to parent ID $parentId")
                // Check if child is the parent itself
                ensure(parentId != childId) {
                    MessageAddChildError.ChildIsParent(parentId, childId)
                }
                // Get the parent message
                val parentMessage = getMessageById(parentId).mapLeft {
                    MessageAddChildError.ParentNotFound(parentId)
                }.bind()
                // Get the child message
                val childMessage = getMessageById(childId).mapLeft {
                    MessageAddChildError.ChildNotFound(childId)
                }.bind()
                // Check if child already exists in parent's children list
                ensure(childId !in parentMessage.childrenMessageIds) {
                    MessageAddChildError.ChildAlreadyExists(parentId, childId)
                }
                // Check if child and parent are in the same session
                ensure(parentMessage.sessionId == childMessage.sessionId) {
                    MessageAddChildError.ChildNotInSession(childId, parentId, parentMessage.sessionId)
                }
                // Update the parent's children list
                val updatedRowCount = ChatMessageTable.update({ ChatMessageTable.id eq parentId }) {
                    it[ChatMessageTable.childrenMessageIds] =
                        Json.encodeToString(parentMessage.childrenMessageIds + childId)
                }
                if (updatedRowCount == 0) {
                    throw IllegalStateException("Failed to update parent message with ID $parentId when adding child $childId")
                }
                logger.debug("DAO: Successfully added child ID $childId to parent ID $parentId")
            }
        }

    /**
     * Helper method to recursively delete a message and all its children.
     * Should only be called from within a transaction block.
     */
    private suspend fun deleteRecursively(messageId: Long) {
        // Get the full message to access its children IDs
        val message = getMessageById(messageId).getOrElse {
            throw IllegalStateException("Failed to retrieve message with ID $messageId")
        }
        // First recursively delete all children
        for (childId in message.childrenMessageIds) {
            deleteRecursively(childId)
        }
        // Then delete this message
        val deletedCount = ChatMessageTable.deleteWhere { ChatMessageTable.id eq messageId }
        if (deletedCount == 0) {
            throw IllegalStateException("Failed to delete message with ID $messageId")
        }
    }

    private fun Raise<InsertMessageError>.verifyParentInSession(parentId: Long, sessionId: Long) {
        val parentSessionId = ChatMessageTable
            .selectAll().where { ChatMessageTable.id eq parentId }
            .singleOrNull()?.get(ChatMessageTable.sessionId)?.value
        ensure(parentSessionId == sessionId) {
            InsertMessageError.ParentNotInSession(parentId, sessionId)
        }
    }
}
```

**9. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt`**
(Updated `processNewMessage` to be non-streaming, added `processNewMessageStreaming`)

```kotlin
package eu.torvian.chatbot.server.service.core
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
import kotlinx.coroutines.flow.Flow

/**
 * Service interface for managing Chat Messages and their threading relationships.
 * Contains core business logic for message processing and modification.
 */
interface MessageService {
    /**
     * Retrieves a list of all messages for a specific session, ordered by creation time.
     * @param sessionId The ID of the session.
     * @return A list of [ChatMessage] objects.
     */
    suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage>

    /**
     * Processes a new incoming user message and awaits the full LLM response.
     * The assistant message is saved only after the full response is received.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to (null for root messages).
     * @return Either a [ProcessNewMessageError] if processing fails,
     *         or a list containing the newly created user and assistant messages ([userMsg, assistantMsg]).
     */
    suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long? = null
    ): Either<ProcessNewMessageError, List<ChatMessage>>

    /**
     * Processes a new incoming user message and streams the LLM response.
     * The user message is saved immediately. The assistant message is saved only upon successful completion of the stream.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to.
     * @return A Flow of Either<ProcessNewMessageError, ChatUpdate>.
     *         The flow emits `ChatUpdate` events (user message, assistant start, deltas, end)
     *         or `ProcessNewMessageError` if an error occurs during streaming.
     */
    fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long? = null
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>>

    /**
     * Updates the content of an existing message.
     * @param id The ID of the message to update.
     * @param content The new content.
     * @return Either an [UpdateMessageContentError] if the message doesn't exist,
     *         or the updated [ChatMessage].
     */
    suspend fun updateMessageContent(id: Long, content: String): Either<UpdateMessageContentError, ChatMessage>

    /**
     * Deletes a specific message and its children recursively.
     * Updates the parent's children list.
     * @param id The ID of the message to delete.
     * @return Either a [DeleteMessageError] if the message doesn't exist, or Unit if successful.
     */
    suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit>
}
```

**10. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt`**
(Refactored `processNewMessage` methods for distinct streaming/non-streaming DB logic)

```kotlin
package eu.torvian.chatbot.server.service.core.impl
import arrow.core.*
import arrow.core.raise.*
import eu.torvian.chatbot.common.api.ApiError // Import for toApiError()
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.*
import eu.torvian.chatbot.server.service.llm.LLMApiClient
import eu.torvian.chatbot.server.service.llm.LLMCompletionError
import eu.torvian.chatbot.server.service.llm.LLMCompletionResult
import eu.torvian.chatbot.server.service.llm.LLMStreamChunk
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
import eu.torvian.chatbot.server.service.core.error.message.*
import eu.torvian.chatbot.server.service.core.error.model.*
import eu.torvian.chatbot.server.service.core.error.provider.*
import eu.torvian.chatbot.server.service.core.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

/**
 * Implementation of the [MessageService] interface.
 * Orchestrates message persistence, threading, and LLM interaction.
 */
class MessageServiceImpl(
    private val messageDao: MessageDao,
    private val sessionDao: SessionDao,
    private val llmModelService: LLMModelService,
    private val modelSettingsService: ModelSettingsService,
    private val llmProviderService: LLMProviderService,
    private val llmApiClient: LLMApiClient,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : MessageService {
    companion object {
        private val logger: Logger = LogManager.getLogger(MessageServiceImpl::class.java)
    }

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
        return transactionScope.transaction {
            messageDao.getMessagesBySessionId(sessionId)
        }
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<ProcessNewMessageError, List<ChatMessage>> =
        transactionScope.transaction {
            either {
                // 1. Validate session
                val session = withError({ daoError: SessionError.SessionNotFound ->
                    ProcessNewMessageError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(sessionId).bind()
                }

                // 2. Save user message immediately
                val userMessage = withError({ daoError: InsertMessageError ->
                    when (daoError) {
                        is InsertMessageError.SessionNotFound -> throw IllegalStateException("Session $sessionId not found after validation")
                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                    }
                }) {
                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
                }
                if (parentMessageId != null) {
                    withError({ daoError: MessageAddChildError ->
                        throw IllegalStateException("Failed to add user message as child to parent: ${daoError.javaClass.simpleName}")
                    }) {
                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind()
                    }
                }

                // 3. Get model and settings config
                val modelId = session.currentModelId ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
                val settingsId = session.currentSettingsId ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))
                val (model, settings, provider, apiKey) = getLlmConfig(session, modelId, settingsId).bind()

                // 4. Build context
                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId)
                val context = buildContext(userMessage, allMessagesInSession)

                // 5. Call LLM API (NON-STREAMING)
                val llmResponseResult = withError({ llmError: LLMCompletionError ->
                    logger.error("LLM API call failed for session $sessionId, provider ${provider.name}: $llmError")
                    ProcessNewMessageError.ExternalServiceError(llmError)
                }) {
                    llmApiClient.completeChat( // Call non-streaming method
                        messages = context,
                        modelConfig = model,
                        provider = provider,
                        settings = settings,
                        apiKey = apiKey
                    ).bind()
                }
                logger.info("LLM API call successful for session $sessionId")

                // 6. Process the LLM response and save the assistant message.
                val assistantMessageContent = llmResponseResult.choices.firstOrNull()?.content ?: run {
                    logger.error("LLM API returned successful response with no choices or empty content for session $sessionId")
                    raise(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.InvalidResponseError("LLM API returned success but no completion choices.")))
                }
                val assistantMessage = withError({ daoError: InsertMessageError ->
                    throw IllegalStateException("Failed to insert assistant message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.insertAssistantMessage(
                        sessionId,
                        assistantMessageContent,
                        userMessage.id, // Parent is user message
                        modelId,
                        settingsId
                    ).bind()
                }

                // 7. Add assistant message as child to user message
                withError({ daoError: MessageAddChildError ->
                    throw IllegalStateException("Failed to add assistant message as child to user message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
                }

                // 8. Update session's leaf message ID
                withError({ daoError: SessionError ->
                    throw IllegalStateException("Failed to update session leaf message ID: ${daoError.javaClass.simpleName}")
                }) {
                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind()
                }

                // 9. Return new messages
                listOf(userMessage, assistantMessage)
            }
        }

    override fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>> = channelFlow { // Use channelFlow for easier control of stream
        // 1. Save user message immediately in its own transaction block.
        // This ensures the user message is persisted even if streaming fails later.
        val userMessage = transactionScope.transaction {
            either {
                val session = sessionDao.getSessionById(sessionId).getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
                val userMsg = messageDao.insertUserMessage(sessionId, content, parentMessageId).bind { daoError ->
                    when (daoError) {
                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                        is InsertMessageError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.sessionId)
                    }
                }
                if (parentMessageId != null) {
                    messageDao.addChildToMessage(parentMessageId, userMsg.id).bind {
                        // Log a warning, but don't fail the whole operation for a linking issue
                        logger.warn("Failed to link user message ${userMsg.id} as child to parent ${parentMessageId}: ${it.message}")
                        ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link user message.").left()
                    }
                }
                userMsg
            }
        }.fold(
            ifLeft = { error ->
                logger.error("Initial user message save failed for session $sessionId: $error")
                send(error.left()) // Send error to client and terminate flow
                close()
                return@channelFlow
            },
            ifRight = { it }
        )

        // 2. Emit the successfully saved user message as the first update
        send(ChatUpdate.UserMessageUpdate(userMessage).right())

        // 3. Get LLM model, settings, provider, and API key
        val (model, settings, provider, apiKey) = either {
            val session = sessionDao.getSessionById(sessionId).getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
            getLlmConfig(session, session.currentModelId ?: -1, session.currentSettingsId ?: -1).bind()
        }.getOrElse { error ->
            logger.error("LLM configuration retrieval failed for session $sessionId: $error")
            send(error.left()) // Send error to client and terminate flow
            close()
            return@channelFlow
        }

        // 4. Build context for LLM call (includes the newly saved user message)
        val allMessagesInSession = transactionScope.transaction { messageDao.getMessagesBySessionId(sessionId) }
        val context = buildContext(userMessage, allMessagesInSession)

        // Generate a temporary ID for the assistant message on the server-side,
        // which will be passed to the client to track the streaming message.
        // The client will then replace this temporary ID with the real DB ID at the end.
        val temporaryAssistantMessageId = -1L // A common convention for "not-yet-persisted" or client-only ID

        // 5. Emit AssistantMessageStart with temporary ID to client
        send(ChatUpdate.AssistantMessageStart(
            messageId = temporaryAssistantMessageId,
            parentId = userMessage.id,
            sessionId = sessionId,
            createdAt = Clock.System.now(), // Client can use this for display
            modelId = model.id,
            settingsId = settings.id
        ).right())

        var accumulatedContent = ""
        var finalUsage: LLMCompletionResult.UsageStats = LLMCompletionResult.UsageStats(0, 0, 0)
        var finalFinishReason: String? = null

        try {
            // 6. Call LLM API in streaming mode and collect chunks
            llmApiClient.completeChatStreaming(context, model, provider, settings, apiKey).collect { llmStreamChunkEither ->
                llmStreamChunkEither.fold(
                    ifLeft = { llmError ->
                        logger.error("LLM API streaming error for session $sessionId, provider ${provider.name}: $llmError")
                        // If an error occurs in the stream, emit it and signal termination
                        send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(llmError).toApiError()).right())
                        throw ProcessNewMessageError.ExternalServiceError(llmError) // Propagate error to cause catch block
                    },
                    ifRight = { chunk ->
                        when (chunk) {
                            is LLMStreamChunk.ContentChunk -> {
                                accumulatedContent += chunk.deltaContent
                                finalFinishReason = chunk.finishReason ?: finalFinishReason // Update finish reason if provided mid-stream
                                send(ChatUpdate.AssistantMessageDelta(temporaryAssistantMessageId, chunk.deltaContent).right())
                            }
                            is LLMStreamChunk.UsageChunk -> {
                                finalUsage = LLMCompletionResult.UsageStats(chunk.promptTokens, chunk.completionTokens, chunk.totalTokens)
                            }
                            LLMStreamChunk.Done -> {
                                // 7. Stream is finished and successful. NOW save the assistant message to the DB.
                                val savedAssistantMessage = transactionScope.transaction {
                                    either {
                                        val assistantMsg = messageDao.insertAssistantMessage(
                                            sessionId,
                                            accumulatedContent,
                                            userMessage.id, // Parent is user message
                                            model.id,
                                            settings.id
                                        ).bind { daoError ->
                                            throw IllegalStateException("Failed to insert final assistant message: ${daoError.javaClass.simpleName}")
                                        }
                                        // Link assistant message as child to user message in DB
                                        messageDao.addChildToMessage(userMessage.id, assistantMsg.id).bind {
                                            logger.warn("Failed to link assistant message ${assistantMsg.id} as child to user message ${userMessage.id}: ${it.message}")
                                            ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link assistant message.").left()
                                        }
                                        // Update session's leaf message ID to the newly saved assistant message
                                        sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id).bind {
                                            logger.warn("Failed to update session leaf message ID to ${assistantMsg.id}: ${it.message}")
                                            ProcessNewMessageError.ModelConfigurationError("Internal error: failed to update session leaf.").left()
                                        }
                                        assistantMsg
                                    }
                                }.getOrElse { error ->
                                    logger.error("Final assistant message save failed for session $sessionId: $error")
                                    send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("Failed to save final message.")).toApiError()).right())
                                    throw ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("Failed to save final message.")) // Propagate error to outer catch
                                }

                                // 8. Emit the final update with the REAL database ID
                                send(ChatUpdate.AssistantMessageEnd(
                                    tempMessageId = temporaryAssistantMessageId,
                                    finalMessage = savedAssistantMessage as ChatMessage.AssistantMessage
                                ).right())
                                send(ChatUpdate.Done.right()) // Signal flow completion
                            }
                            is LLMStreamChunk.Error -> {
                                logger.error("LLM API returned streaming error chunk: ${chunk.llmError}")
                                send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(chunk.llmError).toApiError()).right())
                                throw ProcessNewMessageError.ExternalServiceError(chunk.llmError) // Propagate error
                            }
                        }
                    }
                )
            }
        } catch (e: Exception) {
            // This catches any errors that were `throw`n (including those from LLM API stream)
            logger.error("Streaming message processing failed for session $sessionId due to unhandled exception: ${e.message}", e)
            if (e !is ProcessNewMessageError) { // Ensure we only emit if it's not already a mapped error
                send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("An unexpected error occurred during streaming.", e)).toApiError()).right())
            }
        } finally {
            close() // Ensure the channel is closed after completion or error
        }
    }

    override suspend fun updateMessageContent(
        id: Long,
        content: String
    ): Either<UpdateMessageContentError, ChatMessage> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    UpdateMessageContentError.MessageNotFound(daoError.id)
                }) {
                    messageDao.updateMessageContent(id, content).bind()
                }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    DeleteMessageError.MessageNotFound(daoError.id)
                }) {
                    messageDao.deleteMessage(id).bind()
                }
            }
        }

    private suspend fun Raise<ProcessNewMessageError>.getLlmConfig(
        session: eu.torvian.chatbot.common.models.ChatSession,
        modelId: Long,
        settingsId: Long
    ): Triple<eu.torvian.chatbot.common.models.LLMModel, eu.torvian.chatbot.common.models.ModelSettings, eu.torvian.chatbot.common.models.LLMProvider, String?> {
        val model = withError({ serviceError: GetModelError ->
            ProcessNewMessageError.ModelConfigurationError("Model with ID $modelId not found: ${serviceError.message}")
        }) {
            llmModelService.getModelById(modelId).bind()
        }
        val settings = withError({ serviceError: GetSettingsByIdError ->
            ProcessNewMessageError.ModelConfigurationError("Settings with ID $settingsId not found: ${serviceError.message}")
        }) {
            modelSettingsService.getSettingsById(settingsId).bind()
        }
        val provider = withError({ serviceError: GetProviderError ->
            ProcessNewMessageError.ModelConfigurationError("Provider not found for model ID $modelId (provider ID: ${model.providerId}): ${serviceError.message}")
        }) {
            llmProviderService.getProviderById(model.providerId).bind()
        }
        val apiKey = provider.apiKeyId?.let { keyId ->
            withError({ credError: CredentialError.CredentialNotFound ->
                ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
            }) {
                credentialManager.getCredential(keyId).bind()
            }
        }
        return Triple(model, settings, provider, apiKey)
    }

    /**
     * Builds the context for the LLM API call.
     *
     * @param currentUserMessage The user's message.
     * @param allMessages All messages in the session.
     * @return The context as a list of [ChatMessage] objects.
     */
    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
        val context = mutableListOf<ChatMessage>()
        val messageMap = allMessages.associateBy { it.id }
        var c: ChatMessage? = currentUserMessage
        while (c != null) {
            context.add(0, c)
            c = c.parentMessageId?.let { messageMap[it] }
        }
        return context
    }
}
```

**11. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt`**
(Logic to switch between streaming/non-streaming based on `request.stream`)

```kotlin
package eu.torvian.chatbot.server.ktor.routes
import eu.torvian.chatbot.common.api.ChatbotApiErrorCodes
import eu.torvian.chatbot.common.api.CommonApiErrorCodes
import eu.torvian.chatbot.common.api.apiError
import eu.torvian.chatbot.common.api.resources.SessionResource
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.SessionService
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.session.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import io.ktor.server.sse.* // Import SSE
import kotlinx.serialization.json.Json // Assuming Json is available or injected
import org.koin.ktor.ext.inject // If using Koin, to inject Json

/**
 * Configures routes related to Sessions (/api/v1/sessions) using Ktor Resources.
 */
fun Route.configureSessionRoutes(
    sessionService: SessionService,
    messageService: MessageService
) {
    val json: Json by inject() // Inject the Json instance for serialization

    // GET /api/v1/sessions - List all sessions
    get<SessionResource> {
        call.respond(sessionService.getAllSessionsSummaries())
    }

    // POST /api/v1/sessions - Create a new session
    post<SessionResource> {
        val request = call.receive<CreateSessionRequest>()
        call.respondEither(
            sessionService.createSession(request.name),
            HttpStatusCode.Created
        ) { error ->
            when (error) {
                is CreateSessionError.InvalidName ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid session name provided",
                        "reason" to error.reason
                    )
                is CreateSessionError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid related entity ID provided",
                        "details" to error.message
                    )
            }
        }
    }

    // GET /api/v1/sessions/{sessionId} - Get session by ID
    get<SessionResource.ById> { resource ->
        val sessionId = resource.sessionId
        call.respondEither(sessionService.getSessionDetails(sessionId)) { error ->
            when (error) {
                is GetSessionDetailsError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
            }
        }
    }

    // DELETE /api/v1/sessions/{sessionId} - Delete session by ID
    delete<SessionResource.ById> { resource ->
        val sessionId = resource.sessionId
        call.respondEither(
            sessionService.deleteSession(sessionId),
            HttpStatusCode.NoContent
        ) { error ->
            when (error) {
                is DeleteSessionError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
            }
        }
    }

    // --- Granular PUT routes using nested resources ---
    // PUT /api/v1/sessions/{sessionId}/name - Update the name of a session
    put<SessionResource.ById.Name> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionNameRequest>()
        call.respondEither(sessionService.updateSessionName(sessionId, request.name)) { error ->
            when (error) {
                is UpdateSessionNameError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionNameError.InvalidName ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid session name provided",
                        "reason" to error.reason
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/model - Update the current model ID of a session
    put<SessionResource.ById.Model> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionModelRequest>()
        call.respondEither(
            sessionService.updateSessionCurrentModelId(sessionId, request.modelId)
        ) { error ->
            when (error) {
                is UpdateSessionCurrentModelIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionCurrentModelIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid model ID provided",
                        "modelId" to request.modelId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/settings - Update the current settings ID of a session
    put<SessionResource.ById.Settings> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionSettingsRequest>()
        call.respondEither(
            sessionService.updateSessionCurrentSettingsId(sessionId, request.settingsId)
        ) { error ->
            when (error) {
                is UpdateSessionCurrentSettingsIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionCurrentSettingsIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid settings ID provided",
                        "settingsId" to request.settingsId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/leafMessage - Update the current leaf message ID of a session
    put<SessionResource.ById.LeafMessage> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionLeafMessageRequest>()
        call.respondEither(
            sessionService.updateSessionLeafMessageId(sessionId, request.leafMessageId)
        ) { error ->
            when (error) {
                is UpdateSessionLeafMessageIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionLeafMessageIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid leaf message ID provided",
                        "leafMessageId" to request.leafMessageId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/group - Assign session to group or ungroup
    put<SessionResource.ById.Group> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionGroupRequest>()
        call.respondEither(
            sessionService.updateSessionGroupId(sessionId, request.groupId)
        ) { error ->
            when (error) {
                is UpdateSessionGroupIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionGroupIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid group ID provided",
                        "groupId" to request.groupId.toString()
                    )
            }
        }
    }

    // POST /api/v1/sessions/{sessionId}/messages - Process a new message for a session
    post<SessionResource.ById.Messages> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<ProcessNewMessageRequest>()

        if (request.stream) {
            // Handle Streaming Request
            call.respondSse(eventProducer = {
                messageService.processNewMessageStreaming(
                    sessionId,
                    request.content,
                    request.parentMessageId
                ).collect { eitherUpdate ->
                    eitherUpdate.fold(
                        ifLeft = { error ->
                            val apiError = when (error) {
                                is ProcessNewMessageError.SessionNotFound ->
                                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.sessionId.toString())
                                is ProcessNewMessageError.ParentNotInSession ->
                                    apiError(CommonApiErrorCodes.INVALID_STATE, "Parent message does not belong to this session", "sessionId" to error.sessionId.toString(), "parentId" to error.parentId.toString())
                                is ProcessNewMessageError.ModelConfigurationError ->
                                    apiError(ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR, "LLM configuration error", "details" to error.message)
                                is ProcessNewMessageError.ExternalServiceError ->
                                    apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
                            }
                            // Send an error event to the client
                            send(SseEvent(data = json.encodeToString(ChatUpdate.ErrorUpdate(apiError)), event = "error"))
                            // Close the stream on server-side error
                            close()
                        },
                        ifRight = { chatUpdate ->
                            // Determine the SSE event type based on ChatUpdate sealed class member
                            val eventType = when (chatUpdate) {
                                is ChatUpdate.UserMessageUpdate -> "user_message"
                                is ChatUpdate.AssistantMessageStart -> "assistant_message_start"
                                is ChatUpdate.AssistantMessageDelta -> "assistant_message_delta"
                                is ChatUpdate.AssistantMessageEnd -> "assistant_message_end"
                                is ChatUpdate.ErrorUpdate -> "error" // Redundant if left branch handles, but safe
                                ChatUpdate.Done -> "done"
                            }
                            // Send the ChatUpdate object serialized as JSON
                            send(SseEvent(data = json.encodeToString(chatUpdate), event = eventType))
                            if (chatUpdate == ChatUpdate.Done) {
                                close() // Ensure the SSE connection is closed after sending DONE
                            }
                        }
                    )
                }
            })
        } else {
            // Handle Non-Streaming Request
            call.respondEither(
                messageService.processNewMessage(
                    sessionId,
                    request.content,
                    request.parentMessageId
                ), HttpStatusCode.Created
            ) { error ->
                when (error) {
                    is ProcessNewMessageError.SessionNotFound ->
                        apiError(
                            CommonApiErrorCodes.NOT_FOUND,
                            "Session not found",
                            "sessionId" to error.sessionId.toString()
                        )
                    is ProcessNewMessageError.ParentNotInSession ->
                        apiError(
                            CommonApiErrorCodes.INVALID_STATE,
                            "Parent message does not belong to this session",
                            "sessionId" to error.sessionId.toString(),
                            "parentId" to error.parentId.toString()
                        )
                    is ProcessNewMessageError.ModelConfigurationError ->
                        apiError(
                            ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR,
                            "LLM configuration error",
                            "details" to error.message
                        )
                    is ProcessNewMessageError.ExternalServiceError ->
                        apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
                }
            }
        }
    }
}
```
